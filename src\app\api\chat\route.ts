import { perplexity } from '@ai-sdk/perplexity';
import { streamText, stepCountIs } from 'ai';
import { SYSTEM_PROMPT } from './prompt';
import { getContact } from './tools/getContact';
import { getCrazy } from './tools/getCrazy';
import { getInternship } from './tools/getInternship';
import { getPresentation } from './tools/getPresentation';
import { getProjects } from './tools/getProjects';
import { getResume } from './tools/getResume';
import { getSkills } from './tools/getSkills';
import { getSports } from './tools/getSport';

export const maxDuration = 30;

// API Keys configuration
const PRIMARY_API_KEY = process.env.PERPLEXITY_API_KEY;
const BACKUP_API_KEY = process.env.PERPLEXITY_API_KEY_BACKUP;

// Helper function to create Perplexity model with specific API key
function createPerplexityModel(apiKey: string, modelName: string = 'sonar-pro') {
  const client = perplexity({
    apiKey: apiKey,
  });
  return client(modelName);
}

// Helper function to validate and clean message structure
function validateAndCleanMessages(messages: any[]) {
  const cleanedMessages = [];
  let lastRole = null;

  for (const msg of messages) {
    const role = msg.role;
    const content = msg.parts?.map((part: any) => part.text).join('') || msg.content || '';

    // Skip empty messages
    if (!content.trim()) continue;

    // Ensure alternating pattern (skip consecutive messages from same role)
    if (role === lastRole && role !== 'system') {
      console.log(`[CHAT-API] Skipping consecutive ${role} message to maintain alternating pattern`);
      continue;
    }

    cleanedMessages.push({
      role: role,
      content: content,
    });

    lastRole = role;
  }

  return cleanedMessages;
}

// Error handler function
function errorHandler(error: unknown) {
  if (error == null) {
    return 'Unknown error';
  }
  if (typeof error === 'string') {
    return error;
  }
  if (error instanceof Error) {
    return error.message;
  }
  return JSON.stringify(error);
}

export async function POST(req: Request) {
  try {
    const { messages } = await req.json();
    console.log('[CHAT-API] Incoming messages:', messages);

    // Validate and clean messages to ensure proper alternating pattern
    const cleanedMessages = validateAndCleanMessages(messages);
    console.log('[CHAT-API] Cleaned messages count:', cleanedMessages.length);

    // Format messages for the AI SDK
    const formattedMessages = [
      SYSTEM_PROMPT,
      ...cleanedMessages
    ];

    const tools = {
      getProjects,
      getPresentation,
      getResume,
      getContact,
      getSkills,
      getSports,
      getCrazy,
      getInternship,
    };

    // Try with primary API key first
    let result;
    let lastError;

    try {
      console.log('[CHAT-API] Attempting with primary API key...');
      const primaryModel = createPerplexityModel(PRIMARY_API_KEY!);

      result = streamText({
        model: primaryModel,
        messages: formattedMessages,
        tools,
        stopWhen: stepCountIs(2),
      });

      console.log('[CHAT-API] Primary API key successful');
      return result.toUIMessageStreamResponse();

    } catch (primaryError) {
      console.error('[CHAT-API] Primary API key failed:', primaryError);
      lastError = primaryError;

      // Try with backup API key
      if (BACKUP_API_KEY) {
        try {
          console.log('[CHAT-API] Attempting with backup API key...');
          const backupModel = createPerplexityModel(BACKUP_API_KEY);

          result = streamText({
            model: backupModel,
            messages: formattedMessages,
            tools,
            stopWhen: stepCountIs(2),
          });

          console.log('[CHAT-API] Backup API key successful');
          return result.toUIMessageStreamResponse();

        } catch (backupError) {
          console.error('[CHAT-API] Backup API key also failed:', backupError);
          lastError = backupError;
        }
      } else {
        console.error('[CHAT-API] No backup API key configured');
      }
    }

    // If both keys failed, throw the last error
    throw lastError;

  } catch (err) {
    console.error('[CHAT-API] Global error:', err);
    const errorMessage = errorHandler(err);
    return new Response(JSON.stringify({
      error: errorMessage,
      timestamp: new Date().toISOString(),
      details: 'Both primary and backup API keys failed'
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}
